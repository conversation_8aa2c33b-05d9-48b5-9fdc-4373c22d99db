import { PrismaUserProfile } from './user';
import { Entity } from './entity';

// User preferences interface
export interface UserPreferences {
  id: string;
  user_id: string;
  // Notification preferences
  email_notifications: boolean;
  marketing_emails: boolean;
  weekly_digest: boolean;
  new_tool_alerts: boolean;
  // Privacy preferences
  profile_visibility: 'public' | 'private' | 'friends';
  show_bookmarks: boolean;
  show_reviews: boolean;
  show_activity: boolean;
  // Display preferences
  theme: 'light' | 'dark' | 'system';
  items_per_page: number;
  default_view: 'grid' | 'list';
  // Content preferences
  preferred_categories: string[];
  blocked_categories: string[];
  content_language: string;
  created_at: string;
  updated_at: string;
}

// Tool request interface
export interface ToolRequest {
  id: string;
  user_id: string;
  tool_name: string;
  description: string;
  reason: string;
  category_suggestion?: string;
  website_url?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  admin_notes?: string;
  votes: number;
  created_at: string;
  updated_at: string;
}

// User's submitted tools (entities they've contributed)
export interface UserSubmittedTool {
  id: string;
  entity: Entity;
  submission_status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'PUBLISHED';
  submitted_at: string;
  reviewed_at?: string;
  reviewer_notes?: string;
  changes_requested?: string;
}

// Profile update payload (backend expects snake_case for requests)
export interface ProfileUpdatePayload {
  display_name?: string;
  username?: string;
  bio?: string;
  technical_level?: 'beginner' | 'intermediate' | 'advanced' | 'variable';
  profile_picture_url?: string;
  social_links?: Record<string, string>;
}

// Preferences update payload
export interface PreferencesUpdatePayload {
  email_notifications?: boolean;
  marketing_emails?: boolean;
  weekly_digest?: boolean;
  new_tool_alerts?: boolean;
  profile_visibility?: 'public' | 'private' | 'friends';
  show_bookmarks?: boolean;
  show_reviews?: boolean;
  show_activity?: boolean;
  theme?: 'light' | 'dark' | 'system';
  items_per_page?: number;
  default_view?: 'grid' | 'list';
  preferred_categories?: string[];
  blocked_categories?: string[];
  content_language?: string;
}

// Tool request creation payload
export interface CreateToolRequestPayload {
  toolName: string;
  description: string;
  reason: string;
  categorySuggestion?: string;
  websiteUrl?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH';
}

// Profile statistics
export interface ProfileStats {
  bookmarks_count: number;
  reviews_count: number;
  tools_submitted: number;
  tools_approved: number;
  requests_made: number;
  requests_fulfilled: number;
  reputation_score: number;
  member_since: string;
}

// Complete profile data interface
export interface CompleteProfileData {
  user: PrismaUserProfile;
  preferences: UserPreferences;
  stats: ProfileStats;
  recent_activity: ProfileActivity[];
}

// Profile activity interface
export interface ProfileActivity {
  id: string;
  type: 'BOOKMARK' | 'REVIEW' | 'SUBMISSION' | 'REQUEST' | 'VOTE';
  description: string;
  entity_id?: string;
  entity_name?: string;
  entity_slug?: string;
  created_at: string;
}

// API response types
export interface ProfileResponse {
  success: boolean;
  data: CompleteProfileData;
  message?: string;
}

export interface PreferencesResponse {
  success: boolean;
  data: UserPreferences;
  message?: string;
}

export interface ToolRequestsResponse {
  success: boolean;
  data: ToolRequest[];
  meta: {
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
}

export interface UserSubmittedToolsResponse {
  success: boolean;
  data: UserSubmittedTool[];
  meta: {
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
}
